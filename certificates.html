<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Educasheer Certificate</title>

  <!-- Google Fonts (bullet-proof import) -->
<!-- 1.  Replace the <style>...</style> block with this -->
<style>
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Crimson+Text:wght@400;600;700&display=swap');
  :root{
    --purple-500:#a855f7;
    --blue-500:#3b82f6;
    --cyan-500:#06b6d4;
    --slate-800:#1e293b;
    --amber-400:#fbbf24;
  }
  *{margin:0;padding:0;box-sizing:border-box}
  body{
    font-family:'Inter',sans-serif;
    min-height:100vh;
    display:flex;
    align-items:center;
    justify-content:center;
    padding:20px;
    background:linear-gradient(135deg,#0f172a 0%,#1e293b 40%,#334155 100%);
    overflow-x:hidden;
  }
  .certificate-container{
    position:relative;
    width:100%;
    max-width:900px;
    aspect-ratio:1.414;
    border-radius:24px;
    background:rgba(255,255,255,.92);
    backdrop-filter:blur(20px);
    box-shadow:
      0 25px 50px -12px rgba(0,0,0,.35),
      0 0 0 1px rgba(255,255,255,.35),
      inset 0 1px 0 rgba(255,255,255,.6);
    display:flex;
    align-items:center;
    justify-content:center;
    transition:transform .4s cubic-bezier(.4,0,.2,1);
  }
  @media (prefers-reduced-motion:reduce){
    .certificate-container{transition:none}
  }
  .certificate-container:hover{
    transform:translateY(-6px) rotate(.5deg);
  }
  .certificate-border{
    position:absolute;
    inset:20px;
    border-radius:20px;
    border:3px solid transparent;
    background:
      linear-gradient(135deg,var(--purple-500),var(--blue-500),var(--cyan-500)) border-box;
    -webkit-mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite:destination-out;
    mask-composite:exclude;
    animation:borderGlow 6s ease-in-out infinite alternate;
  }
  @keyframes borderGlow{
    0%{filter:brightness(1)}
    100%{filter:brightness(1.25)}
  }
  .certificate-content{
    position:relative;
    z-index:2;
    padding:50px;
    width:100%;
    height:100%;
    display:flex;
    flex-direction:column;
    align-items:center;
    text-align:center;
  }
  .logo{
    width:100px;
    height:100px;
    border-radius:50%;
    background:linear-gradient(135deg,var(--purple-500),var(--blue-500));
    display:flex;
    align-items:center;
    justify-content:center;
    box-shadow:0 10px 25px rgba(168,85,247,.4);
    margin-bottom:15px;
    overflow:hidden;          /* keep round shape */
  }
  .logo img{
    width:65%;
    height:65%;
    object-fit:contain;
    object-position:center;   /* center the asset */
  }
  .company-name{
    font-family:'Crimson Text',serif;
    font-size:clamp(28px,4vw,38px);
    font-weight:700;
    letter-spacing:.1em;
    color:var(--slate-800);
  }
  .certificate-title{
    margin:20px 0 30px;
    font-size:clamp(16px,2.5vw,20px);
    font-weight:700;
    letter-spacing:.2em;
    text-transform:uppercase;
    color:var(--blue-500);
  }
  .recipient-name{
    font-family:'Crimson Text',serif;
    font-size:clamp(36px,5vw,52px);
    font-weight:700;
    color:var(--slate-800);
    position:relative;
  }
  .recipient-name::after{
    content:'';
    position:absolute;
    left:0;
    bottom:-8px;
    width:100%;
    height:3px;
    border-radius:2px;
    background:linear-gradient(135deg,var(--purple-500),var(--blue-500));
  }
  .completion-text{
    margin:25px 0 15px;
    font-size:clamp(18px,2.5vw,22px);
    font-weight:600;
    color:#334155;
  }
  .course-name{
    font-family:'Crimson Text',serif;
    font-size:clamp(28px,4vw,36px);
    font-weight:700;
    background:linear-gradient(135deg,var(--purple-500),var(--blue-500));
    -webkit-background-clip:text;
    -webkit-text-fill-color:transparent;
    background-clip:text;
  }
  .stars{display:flex;gap:8px;margin:25px 0}
  .star{
    width:30px;
    height:30px;
    clip-path:polygon(50% 0%,61% 35%,98% 35%,68% 57%,79% 91%,50% 70%,21% 91%,32% 57%,2% 35%,39% 35%);
    background:linear-gradient(var(--amber-400),#f59e0b);
    animation:twinkle 3s ease-in-out infinite;
  }
  .star:nth-child(2){animation-delay:.3s}
  .star:nth-child(3){animation-delay:.6s}
  .star:nth-child(4){animation-delay:.9s}
  .star:nth-child(5){animation-delay:1.2s}
  @keyframes twinkle{
    0%,100%{transform:scale(1) rotate(0deg)}
    50%{transform:scale(1.25) rotate(180deg)}
  }
  .supervisor-section{
    margin:25px 0 35px;
    padding:20px clamp(15px,2vw,35px);
    border-radius:16px;
    background:rgba(255,255,255,.6);
    box-shadow:0 8px 20px rgba(0,0,0,.08);
    max-width:90%;
  }
  .supervisor-name{
    font-family:'Crimson Text',serif;
    font-size:clamp(24px,3.5vw,32px);
    font-weight:700;
    color:var(--slate-800);
  }
  .supervisor-title-role{
    font-size:clamp(16px,2.5vw,20px);
    font-weight:600;
    font-style:italic;
    color:#475569;
  }
  .footer-section{
    width:100%;
    display:flex;
    justify-content:space-between;
    align-items:center;
    border-top:1px solid rgba(0,0,0,.08);
    padding-top:20px;
    font-weight:600;
    color:#334155;
  }
  .signature-line{width:160px;height:2px;background:#334155;margin-bottom:5px;border-radius:2px}
  .editable{
    cursor:pointer;
    background:#fef3c7;
    padding:2px 8px;
    border-radius:4px;
    transition:.2s;
  }
  .editable:hover{background:#fde68a}
  @media(max-width:600px){
    .certificate-content{padding:35px 25px}
    .footer-section{flex-direction:column;gap:15px}
  }
</style>
</head>

<body>
  <div class="certificate-container">
    <div class="certificate-border"></div>

    <div class="certificate-content">
      <div class="logo">
        <!-- Replace with your actual logo -->
        <img src="logo.png" alt="Educasheer Logo" />
      </div>
      <div class="company-name">EDUCASHEER</div>

      <h2 class="certificate-title">Certificate of Completion</h2>

      <p class="recipient-name">
        <span class="editable" onclick="editField(this)">John Doe</span>
      </p>

      <p class="completion-text">has successfully completed the course</p>

      <p class="course-name">
        <span class="editable" onclick="editField(this)">Advanced Web Development</span>
      </p>

      <div class="stars">
        <div class="star"></div><div class="star"></div><div class="star"></div><div class="star"></div><div class="star"></div>
      </div>

      <div class="supervisor-section">
        <div>Under the supervision of</div>
        <div class="supervisor-name">Musavir Khaliq</div>
        <div class="supervisor-title-role">Research Scientist</div>
      </div>

      <footer class="footer-section">
        <span>
          Completed on: <span class="editable" onclick="editField(this)">December 15, 2024</span>
        </span>
        <div>
          <div class="signature-line"></div>
          <small>Authorized Signature</small>
        </div>
      </footer>
    </div>
  </div>

  <script>
    /* Inline editing helper */
    function editField(el){
      const original = el.textContent;
      const input = document.createElement('input');
      input.type = 'text';
      input.value = original;
      input.style.cssText = `
        width:100%;font:inherit;border:1px solid #3b82f6;
        border-radius:4px;padding:2px 6px;background:#fff;color:#1e293b;
        outline:none;text-align:center;
      `;
      el.replaceWith(input); input.focus();
      const save = () => {
        const span = document.createElement('span');
        span.className = 'editable';
        span.textContent = input.value.trim() || original;
        span.onclick = () => editField(span);
        input.replaceWith(span);
      };
      input.addEventListener('blur', save);
      input.addEventListener('keydown', e => e.key === 'Enter' && save());
    }
  </script>
</body>
</html>